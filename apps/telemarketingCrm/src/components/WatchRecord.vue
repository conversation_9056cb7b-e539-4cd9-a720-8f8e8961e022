<template>
  <div class="watch-record">
    <div v-if="data.length > 0"
         class="list">
      <div v-for="(item, index) in data"
           :key="`video-${index}`"
           class="list-item-row">
        <div class="list-item-cell">
          <div class="label">视频名称</div>
          <div class="value">{{ item.video?.name || '' }}</div>
        </div>
        <div class="list-item-cell">
          <div class="label">知识点名称</div>
          <div class="value">{{ item.topic?.name || '' }}</div>
        </div>
        <div class="list-item-cell">
          <div class="label">是否付费内容</div>
          <div class="value">
            {{
              item.topic?.isFreeTime ? '限免' : item.topic.pay ? '付费' : '免费'
            }}
          </div>
        </div>
        <div class="list-item-cell">
          <div class="label">观看时间</div>
          <div class="value">
            {{ dayjs(item.timestamp * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </div>
        </div>
      </div>
    </div>
    <van-empty v-else
               description="暂无记录" />
  </div>
</template>

<script setup>
import { closeToast, Empty, showLoadingToast, showToast } from 'vant'
import { getWatchRecordApi } from '@/api/index'
import { formatDate } from '@/utils/wecom'
import dayjs from 'dayjs'

defineProps({
  // eslint-disable-next-line vue/no-unused-properties
  userId: {
    type: String,
    default: '',
  },
})

defineOptions({
  components: {
    'van-empty': Empty,
  },
  data() {
    return {
      data: [],
    }
  },
  methods: {
    formatDate,
    getWatchRecord() {
      showLoadingToast()
      getWatchRecordApi(this.userId)
        .then((res) => {
          closeToast()
          this.data = res
        })
        .catch(() => {
          this.data = []
          showToast('观看记录加载失败')
        })
    },
  },
  created() {
    this.getWatchRecord()
  },
})
</script>

<style lang="scss" scoped>
.watch-record {
  padding: 30px;

  .tips {
    display: flex;
    gap: 15px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #eaf5ff;

    &-icon {
      margin-top: 5px;
      font-size: 40px;
      vertical-align: middle;
    }
  }

  .list {
    .list-item-row {
      padding: 20px 30px;
      padding-bottom: 0;
      margin-bottom: 20px;
      border: 1px solid #ccc;

      .list-item-cell {
        display: flex;
        margin-bottom: 20px;

        .label {
          width: 220px;
        }

        .value {
          flex: 1;
        }
      }
    }
  }
}
</style>
