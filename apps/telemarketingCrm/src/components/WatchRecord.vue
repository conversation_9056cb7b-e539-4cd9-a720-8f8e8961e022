<template>
  <div class="watch-record">
    <div v-if="allData.length > 0"
         class="list">
      <div v-for="(item, index) in paginatedData"
           :key="`video-${index}`"
           class="list-item-row">
        <div class="list-item-cell">
          <div class="label">视频名称</div>
          <div class="value">{{ item.video?.name || '' }}</div>
        </div>
        <div class="list-item-cell">
          <div class="label">知识点名称</div>
          <div class="value">{{ item.topic?.name || '' }}</div>
        </div>
        <div class="list-item-cell">
          <div class="label">是否付费内容</div>
          <div class="value">
            {{
              item.topic?.isFreeTime ? '限免' : item.topic?.pay ? '付费' : '免费'
            }}
          </div>
        </div>
        <div class="list-item-cell">
          <div class="label">观看时间</div>
          <div class="value">
            {{ dayjs(item.timestamp * 1000).format('YYYY-MM-DD HH:mm:ss') }}
          </div>
        </div>
      </div>

      <!-- Element Plus 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination v-model:current-page="currentPage"
                       v-model:page-size="pageSize"
                       :page-sizes="[5, 10, 20, 50]"
                       :small="false"
                       :disabled="loading"
                       :background="true"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="allData.length"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange" />
      </div>
    </div>

    <el-empty v-else
              description="暂无记录" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElEmpty, ElPagination } from 'element-plus'
import { getWatchRecordApi } from '@/api/index'
import dayjs from 'dayjs'

// 定义数据类型
interface WatchRecordItem {
  video?: {
    name?: string
  }
  topic?: {
    name?: string
    isFreeTime?: boolean
    pay?: boolean
  }
  timestamp: number
}

// 定义 props
const props = defineProps({
  userId: {
    type: String,
    default: '',
  },
})

// 响应式数据
const allData = ref<WatchRecordItem[]>([]) // 所有数据
const currentPage = ref(1) // 当前页码
const pageSize = ref(10) // 每页显示数量
const loading = ref(false) // 加载状态

// 计算属性：当前页显示的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return allData.value.slice(start, end)
})

// 获取观看记录
const getWatchRecord = async () => {
  if (!props.userId) return

  loading.value = true
  try {
    const res = await getWatchRecordApi(props.userId)
    allData.value = (res as WatchRecordItem[]) || []
  } catch (error) {
    allData.value = []
    ElMessage.error('观看记录加载失败')
  } finally {
    loading.value = false
  }
}

// 处理每页显示数量变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1 // 重置到第一页
}

// 处理当前页变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 组件挂载时获取数据
onMounted(() => {
  getWatchRecord()
})
</script>

<style lang="scss" scoped>
.watch-record {
  padding: 30px;

  .tips {
    display: flex;
    gap: 15px;
    padding: 20px;
    margin-bottom: 20px;
    background-color: #eaf5ff;

    &-icon {
      margin-top: 5px;
      font-size: 40px;
      vertical-align: middle;
    }
  }

  .list {
    .list-item-row {
      padding: 20px 30px;
      padding-bottom: 0;
      margin-bottom: 20px;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease;

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      .list-item-cell {
        display: flex;
        margin-bottom: 20px;

        .label {
          width: 220px;
          font-weight: 500;
          color: #606266;
        }

        .value {
          flex: 1;
          color: #303133;
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding: 20px 0;
      margin-top: 30px;
    }
  }
}

/* Element Plus 分页组件样式调整 */
.watch-record :deep(.el-pagination) {
  .el-pagination__total {
    color: #606266;
  }

  .el-pagination__sizes {
    .el-select {
      .el-input__inner {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
  }

  .el-pagination__jump {
    color: #606266;

    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }
}
</style>
