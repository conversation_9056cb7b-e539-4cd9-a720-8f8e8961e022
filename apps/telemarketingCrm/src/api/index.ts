/*
 * @Date         : 2024-07-19 16:33:49
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import instance from './axios'

// 通过code换取token
export function getTokenApi(data) {
  return instance.post(`/wuhan-miniprogram/wecom/token`, data)
}

// 获取签名
export function getWeComApi(data) {
  return instance.post(`/wuhan-miniprogram/wecom/jsapi/signed`, data)
}

// 获取用户信息
export function getUserIdApi(id) {
  return instance.get(`/wuhan-miniprogram/wecomUser/exchange/${id}`)
}

// 获取用户详情
export function getUserInfoApi(data) {
  return instance.post(
    `/wuhan-datapool/workerAllocateInfo/takeByCurrentUser`,
    data,
  )
}

// 获取用户位置信息
export function getUserRegionApi(params) {
  return instance.get(`/scrm/user/region`, params)
}

// 获取观看记录
export function getWatchRecordApi(params) {
  return instance.get(`/scrm/customer/video/history/${params.id}`, params)
}

// 获取跟单记录
export function getCallRecordApi(data) {
  return instance.post(`/scrm/customer/call/record/list`, data)
}

/**
 * @description: 获取用户信息记录
 * @param { string } infoUuid
 * @returns { string } note
 */
export const getUserRecordApi = (params) => {
  return instance.get(`/wuhan-datapool/info/GetInfoDescription`, params)
}

/**
 * @description: 设置用户信息记录
 * @param { string } infoUuid
 * @param { string } description
 * @returns { string } description
 */
export const setUserRecordApi = (data) => {
  return instance.post(`/wuhan-datapool/info/SaveInfoDescription`, data)
}

/**
 * @description: 获取学情报告的用户token
 * @param { string } userId
 * @returns { string } description
 */
export function getIframeTokenApi(params: {
  userId: string
  [k: string]: unknown
}): Promise<{ token: string; userId: string }> {
  return instance.get(`/web/proxy/user/statistic_study_token`, params)
}

// 获取意向度字典表
export function intentionApi() {
  return instance.get(`/web/customer/intention/list`)
}
